import React, { useState, useEffect } from 'react';
import { 
  Modal, Form, Input, InputNumber, Button, Space, 
  Card, Divider, Row, Col, message, Select, 
  Typography, Switch, Radio, Alert, Tooltip
} from 'antd';
import {
  ContainerOutlined, SettingOutlined,
  CloudOutlined, AppstoreOutlined,
  QuestionCircleOutlined, PlusOutlined, DeleteOutlined
} from '@ant-design/icons';
import useContainerConfig from '@/hooks/useContainerConfig';

const { Text } = Typography;
const { Option } = Select;
const { TextArea } = Input;

export interface ContainerConfigEditorProps {
  visible: boolean;
  appData: API.AppData | null;
  onCancel: () => void;
  onSave: (updatedSettings: any) => Promise<boolean>;
}

const ContainerConfigEditor: React.FC<ContainerConfigEditorProps> = ({
  visible,
  appData,
  onCancel,
  onSave
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const { containerConfig, saveContainerConfig } = useContainerConfig();

  useEffect(() => {
    if (visible && containerConfig) {
      form.setFieldsValue({
        ...containerConfig,
        // 确保嵌套对象正确设置
        resources: containerConfig.resources || {
          requests: { cpu: '500m', memory: '512Mi' },
          limits: { cpu: '1000m', memory: '1Gi' }
        },
        ports: containerConfig.ports || [
          { name: 'http', container_port: 8080, service_port: 8080, protocol: 'TCP' }
        ],
        rollout_config: containerConfig.rollout_config || {
          max_surge: '25%',
          max_unavailable: '25%',
          revision_history_limit: 10,
          timeout_seconds: 600
        }
      });
    }
  }, [visible, containerConfig, form]);

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);
      
      const success = await saveContainerConfig(values);
      if (success) {
        message.success('容器配置已成功保存');
        onCancel();
      }
    } catch (error) {
      console.error('表单验证失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const addPort = () => {
    const ports = form.getFieldValue('ports') || [];
    ports.push({
      name: `port-${ports.length + 1}`,
      container_port: 8080,
      service_port: 8080,
      protocol: 'TCP'
    });
    form.setFieldsValue({ ports });
  };

  const removePort = (index: number) => {
    const ports = form.getFieldValue('ports') || [];
    if (ports.length > 1) {
      ports.splice(index, 1);
      form.setFieldsValue({ ports });
    }
  };

  if (!appData) {
    return null;
  }

  return (
    <Modal
      title={
        <Space>
          <ContainerOutlined />
          <span>容器配置</span>
        </Space>
      }
      open={visible}
      width={900}
      onCancel={onCancel}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          取消
        </Button>,
        <Button 
          key="submit" 
          type="primary" 
          loading={loading} 
          onClick={handleSubmit}
        >
          保存配置
        </Button>
      ]}
      destroyOnClose
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={containerConfig || undefined}
      >
        {/* 基础配置 */}
        <Card 
          title={
            <Space>
              <SettingOutlined />
              <Text strong>基础配置</Text>
            </Space>
          }
          size="small" 
          style={{ marginBottom: 16 }}
          variant="borderless"
        >
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="replicas"
                label="副本数"
                rules={[
                  { required: true, message: '请输入副本数' },
                  { type: 'integer', min: 1, max: 10, message: '副本数必须在1-10之间' }
                ]}
              >
                <InputNumber 
                  min={1} 
                  max={10} 
                  style={{ width: '100%' }}
                  placeholder="请输入副本数"
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="namespace"
                label="命名空间"
                rules={[{ required: true, message: '请输入命名空间' }]}
              >
                <Select placeholder="请选择命名空间">
                  <Option value="default">default</Option>
                  <Option value="production">production</Option>
                  <Option value="staging">staging</Option>
                  <Option value="testing">testing</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="strategy"
                label="部署策略"
                rules={[{ required: true, message: '请选择部署策略' }]}
              >
                <Select placeholder="请选择部署策略">
                  <Option value="RollingUpdate">滚动更新</Option>
                  <Option value="Recreate">重新创建</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="base_image"
            label="基础镜像"
            rules={[{ required: true, message: '请输入基础镜像' }]}
          >
            <Input placeholder="请输入基础镜像，如: openjdk:17-jre-slim" />
          </Form.Item>
        </Card>

        {/* 端口配置 */}
        <Card 
          title={
            <Space>
              <AppstoreOutlined />
              <Text strong>端口配置</Text>
              <Tooltip title="配置容器对外暴露的端口">
                <QuestionCircleOutlined style={{ color: '#8c8c8c' }} />
              </Tooltip>
            </Space>
          }
          size="small" 
          style={{ marginBottom: 16 }}
          variant="borderless"
          extra={
            <Button
              type="dashed"
              size="small"
              icon={<PlusOutlined />}
              onClick={addPort}
            >
              添加端口
            </Button>
          }
        >
          <Form.List name="ports">
            {(fields, { add, remove }) => (
              <>
                {fields.map(({ key, name, ...restField }, index) => (
                  <Row key={key} gutter={16} style={{ marginBottom: 8 }}>
                    <Col span={5}>
                      <Form.Item
                        {...restField}
                        name={[name, 'name']}
                        label={index === 0 ? '端口名称' : ''}
                        rules={[{ required: true, message: '请输入端口名称' }]}
                      >
                        <Input placeholder="如: http" />
                      </Form.Item>
                    </Col>
                    <Col span={5}>
                      <Form.Item
                        {...restField}
                        name={[name, 'container_port']}
                        label={index === 0 ? '容器端口' : ''}
                        rules={[{ required: true, message: '请输入容器端口' }]}
                      >
                        <InputNumber 
                          min={1} 
                          max={65535} 
                          style={{ width: '100%' }}
                          placeholder="8080"
                        />
                      </Form.Item>
                    </Col>
                    <Col span={5}>
                      <Form.Item
                        {...restField}
                        name={[name, 'service_port']}
                        label={index === 0 ? '服务端口' : ''}
                        rules={[{ required: true, message: '请输入服务端口' }]}
                      >
                        <InputNumber 
                          min={1} 
                          max={65535} 
                          style={{ width: '100%' }}
                          placeholder="8080"
                        />
                      </Form.Item>
                    </Col>
                    <Col span={5}>
                      <Form.Item
                        {...restField}
                        name={[name, 'protocol']}
                        label={index === 0 ? '协议' : ''}
                        rules={[{ required: true, message: '请选择协议' }]}
                      >
                        <Select placeholder="协议">
                          <Option value="TCP">TCP</Option>
                          <Option value="UDP">UDP</Option>
                        </Select>
                      </Form.Item>
                    </Col>
                    <Col span={4}>
                      {index === 0 && <div style={{ marginBottom: 24 }}></div>}
                      {fields.length > 1 && (
                        <Button
                          type="text"
                          danger
                          icon={<DeleteOutlined />}
                          onClick={() => remove(name)}
                        />
                      )}
                    </Col>
                  </Row>
                ))}
              </>
            )}
          </Form.List>
        </Card>

        {/* 资源配置 */}
        <Card 
          title={
            <Space>
              <CloudOutlined />
              <Text strong>资源配置</Text>
            </Space>
          }
          size="small" 
          style={{ marginBottom: 16 }}
          variant="borderless"
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name={['resources', 'requests', 'cpu']}
                label="CPU请求"
                rules={[{ required: true, message: '请输入CPU请求' }]}
              >
                <Input placeholder="如: 500m" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name={['resources', 'limits', 'cpu']}
                label="CPU限制"
                rules={[{ required: true, message: '请输入CPU限制' }]}
              >
                <Input placeholder="如: 1000m" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name={['resources', 'requests', 'memory']}
                label="内存请求"
                rules={[{ required: true, message: '请输入内存请求' }]}
              >
                <Input placeholder="如: 512Mi" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name={['resources', 'limits', 'memory']}
                label="内存限制"
                rules={[{ required: true, message: '请输入内存限制' }]}
              >
                <Input placeholder="如: 1Gi" />
              </Form.Item>
            </Col>
          </Row>
        </Card>

        {/* 发布配置 */}
        <Card 
          title={
            <Space>
              <AppstoreOutlined />
              <Text strong>发布配置</Text>
            </Space>
          }
          size="small" 
          style={{ marginBottom: 16 }}
          variant="borderless"
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name={['rollout_config', 'max_surge']}
                label="最大激增"
                rules={[{ required: true, message: '请输入最大激增' }]}
              >
                <Input placeholder="如: 25%" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name={['rollout_config', 'max_unavailable']}
                label="最大不可用"
                rules={[{ required: true, message: '请输入最大不可用' }]}
              >
                <Input placeholder="如: 25%" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name={['rollout_config', 'revision_history_limit']}
                label="历史版本限制"
              >
                <InputNumber 
                  min={1} 
                  max={100} 
                  style={{ width: '100%' }}
                  placeholder="默认10"
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name={['rollout_config', 'timeout_seconds']}
                label="超时时间(秒)"
              >
                <InputNumber 
                  min={60} 
                  max={3600} 
                  style={{ width: '100%' }}
                  placeholder="默认600"
                />
              </Form.Item>
            </Col>
          </Row>
        </Card>

        {/* 环境变量配置 */}
        <Card 
          title={
            <Space>
              <SettingOutlined />
              <Text strong>环境变量</Text>
            </Space>
          }
          size="small" 
          style={{ marginBottom: 16 }}
          variant="borderless"
        >
          <Form.Item
            name="environment"
            label="环境变量 (JSON格式)"
          >
            <TextArea 
              rows={4} 
              placeholder='{"ENV_NAME": "value", "DEBUG": "true"}'
            />
          </Form.Item>
        </Card>
      </Form>
    </Modal>
  );
};

export default ContainerConfigEditor; 