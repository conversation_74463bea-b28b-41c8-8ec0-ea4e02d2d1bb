/* ContainerConfigTooltip 组件样式 */

.container-config-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  
  &:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
  }
  
  .ant-descriptions {
    .ant-descriptions-item-label {
      font-weight: 500;
      color: #262626;
      background-color: #fafafa;
      border-right: 1px solid #f0f0f0;
    }
    
    .ant-descriptions-item-content {
      background-color: white;
      padding: 12px 16px;
    }
  }
}

/* 配置状态指示器 */
.config-status-indicator {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  
  &.success {
    background-color: #f6ffed;
    color: #389e0d;
    border: 1px solid #b7eb8f;
  }
  
  &.warning {
    background-color: #fffbe6;
    color: #d48806;
    border: 1px solid #ffe58f;
  }
  
  &.error {
    background-color: #fff2f0;
    color: #cf1322;
    border: 1px solid #ffccc7;
  }
}

/* 资源配置卡片 */
.resource-config-card {
  border-radius: 6px;
  transition: all 0.2s ease;
  
  &.requests {
    background-color: #f6ffed;
    border: 1px solid #b7eb8f;
    
    &:hover {
      background-color: #f0f9e8;
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(82, 196, 26, 0.15);
    }
  }
  
  &.limits {
    background-color: #fff2e8;
    border: 1px solid #ffbb96;
    
    &:hover {
      background-color: #ffeee0;
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(250, 140, 22, 0.15);
    }
  }
}

/* 端口配置项 */
.port-config-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background-color: #f0f5ff;
  border-radius: 6px;
  border: 1px solid #adc6ff;
  transition: all 0.2s ease;
  
  &:hover {
    background-color: #e6f7ff;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);
  }
  
  .port-icon {
    color: #1890ff;
    margin-right: 8px;
    font-size: 14px;
  }
  
  .port-info {
    flex: 1;
    display: flex;
    align-items: center;
    gap: 8px;
  }
}

/* 复制按钮样式 */
.copyable-content {
  display: flex;
  align-items: center;
  gap: 8px;
  
  .copy-button {
    opacity: 0;
    transition: opacity 0.2s ease;
    
    &:hover {
      background-color: #f5f5f5;
    }
  }
  
  &:hover .copy-button {
    opacity: 1;
  }
}

/* 表单卡片样式 */
.form-section-card {
  margin-bottom: 16px;
  border-radius: 8px;
  
  .ant-card-head {
    background-color: #fafafa;
    border-bottom: 1px solid #f0f0f0;
    border-radius: 8px 8px 0 0;
    
    .ant-card-head-title {
      font-size: 14px;
      font-weight: 500;
    }
  }
  
  .ant-card-body {
    background-color: white;
    border-radius: 0 0 8px 8px;
  }
}

/* 表单项增强样式 */
.enhanced-form-item {
  .ant-form-item-label {
    .form-label-with-tooltip {
      display: flex;
      align-items: center;
      gap: 4px;
      
      .tooltip-icon {
        color: #1890ff;
        cursor: help;
        
        &:hover {
          color: #40a9ff;
        }
      }
    }
  }
}

/* 模态框样式增强 */
.container-config-modal {
  .ant-modal-header {
    background-color: #fafafa;
    border-bottom: 1px solid #f0f0f0;
    border-radius: 8px 8px 0 0;
  }
  
  .ant-modal-body {
    background-color: #fcfcfc;
    padding: 24px;
  }
  
  .ant-modal-footer {
    background-color: #fafafa;
    border-top: 1px solid #f0f0f0;
    border-radius: 0 0 8px 8px;
  }
}

/* 加载状态样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 24px;
  
  .loading-text {
    margin-top: 16px;
    color: #8c8c8c;
    font-size: 14px;
  }
}

/* 空状态样式 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 24px;
  
  .empty-icon {
    font-size: 48px;
    color: #faad14;
    margin-bottom: 16px;
  }
  
  .empty-text {
    color: #8c8c8c;
    font-size: 14px;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .container-config-card {
    .ant-descriptions {
      .ant-descriptions-item {
        padding-bottom: 12px;
      }
      
      .ant-descriptions-item-label {
        width: 100px;
        font-size: 12px;
      }
      
      .ant-descriptions-item-content {
        padding: 8px 12px;
        font-size: 13px;
      }
    }
  }
  
  .resource-config-card {
    margin-bottom: 12px;
    
    .ant-form-item {
      margin-bottom: 8px;
    }
  }
  
  .port-config-item {
    padding: 6px 10px;
    
    .port-info {
      flex-direction: column;
      align-items: flex-start;
      gap: 4px;
    }
  }
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.container-config-card {
  animation: fadeInUp 0.3s ease-out;
}

.form-section-card {
  animation: fadeInUp 0.3s ease-out;
}

/* 深色主题支持 */
@media (prefers-color-scheme: dark) {
  .container-config-card {
    background-color: #1f1f1f;
    border-color: #434343;
    
    .ant-descriptions-item-label {
      background-color: #262626;
      color: #f0f0f0;
      border-color: #434343;
    }
    
    .ant-descriptions-item-content {
      background-color: #1f1f1f;
      color: #f0f0f0;
    }
  }
  
  .resource-config-card {
    &.requests {
      background-color: #162312;
      border-color: #389e0d;
    }
    
    &.limits {
      background-color: #2b1d11;
      border-color: #d46b08;
    }
  }
  
  .port-config-item {
    background-color: #111a2c;
    border-color: #1890ff;
  }
}
