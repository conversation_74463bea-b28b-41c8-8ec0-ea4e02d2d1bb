import React, { useState } from 'react';
import { 
  Button, Descriptions, Typography, Space, Form, InputNumber, Input, Modal, 
  Row, Col, Divider, Select, Tag
} from 'antd';
import { 
  EditOutlined, SaveOutlined, CloseOutlined, PlusOutlined, DeleteOutlined
} from '@ant-design/icons';
import useContainerConfig from '@/hooks/useContainerConfig';

const { Title, Text } = Typography;
const { Option } = Select;

interface ContainerConfigTooltipProps {
  onEdit?: () => void;
  editable?: boolean;
  onSave?: (updatedSettings: any) => Promise<boolean>;
}

const ContainerConfigTooltip: React.FC<ContainerConfigTooltipProps> = ({
  onEdit,
  editable = false
}) => {
  const { 
    containerConfig, 
    loading, 
    saveContainerConfig, 
    setContainerConfig 
  } = useContainerConfig();
  
  const [isEditing, setIsEditing] = useState(false);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [form] = Form.useForm<API.Container>();
  const [saving, setSaving] = useState(false);

  // 处理编辑按钮点击
  const handleEdit = () => {
    if (onEdit) {
      onEdit();
    } else {
      if (containerConfig) {
        // 设置表单初始值
        form.setFieldsValue({
          ...containerConfig,
          // 确保端口配置正确设置
          ports: containerConfig.ports || [{ name: 'http', container_port: 8080, service_port: 8080, protocol: 'TCP' }],
          // 确保资源配置正确设置
          resources: containerConfig.resources || {
            requests: { cpu: '500m', memory: '512Mi' },
            limits: { cpu: '1000m', memory: '1Gi' }
          }
        });
        setEditModalVisible(true);
        setIsEditing(true);
      }
    }
  };

  // 处理保存
  const handleSave = async () => {
    try {
      setSaving(true);
      const values = await form.validateFields();
      
      const success = await saveContainerConfig(values);
      if (success) {
        setEditModalVisible(false);
        setIsEditing(false);
      }
    } catch (error) {
      console.error('保存容器配置失败:', error);
    } finally {
      setSaving(false);
    }
  };

  // 处理取消
  const handleCancel = () => {
    setEditModalVisible(false);
    setIsEditing(false);
    form.resetFields();
  };

  // 格式化资源配置显示
  const formatResourceConfig = (requests: API.ResourceSpec, limits: API.ResourceSpec) => (
    <div>
      <div>
        <Text type="secondary">请求:</Text> CPU {requests.cpu} / 内存 {requests.memory}
      </div>
      <div>
        <Text type="secondary">限制:</Text> CPU {limits.cpu} / 内存 {limits.memory}
      </div>
    </div>
  );

  // 格式化端口配置显示
  const formatPortsConfig = (ports: API.PortConfig[]) => (
    <Space direction="vertical" size="small">
      {ports.map((port, index) => (
        <div key={index}>
          <Tag color="blue">{port.name}</Tag>
          <Text>{port.container_port}:{port.service_port}/{port.protocol}</Text>
        </div>
      ))}
    </Space>
  );

  if (loading) {
    return <div>容器配置加载中...</div>;
  }

  if (!containerConfig) {
    return <div>容器配置未找到</div>;
  }

  return (
    <div>
      <div style={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center', 
        marginBottom: 12 
      }}>
        <Title level={5} style={{ margin: 0 }}>
          容器配置
        </Title>
        {editable && (
          <Button 
            type="primary" 
            size="small" 
            icon={<EditOutlined />} 
            onClick={handleEdit}
          >
            编辑
          </Button>
        )}
      </div>
      
      <Descriptions column={1} size="small" bordered style={{ backgroundColor: 'white' }}>
        <Descriptions.Item label="副本数">
          {containerConfig.replicas}
        </Descriptions.Item>
        <Descriptions.Item label="命名空间">
          {containerConfig.namespace}
        </Descriptions.Item>
        <Descriptions.Item label="基础镜像">
          <code style={{ 
            background: '#f5f5f5', 
            padding: '2px 6px', 
            borderRadius: '3px',
            fontSize: '12px' 
          }}>
            {containerConfig.base_image}
          </code>
        </Descriptions.Item>
        <Descriptions.Item label="端口配置">
          {formatPortsConfig(containerConfig.ports || [])}
        </Descriptions.Item>
        <Descriptions.Item label="资源配置">
          {containerConfig.resources ? 
            formatResourceConfig(containerConfig.resources.requests, containerConfig.resources.limits) :
            <Text type="secondary">未配置</Text>
          }
        </Descriptions.Item>
        <Descriptions.Item label="部署策略">
          <Tag color="green">{containerConfig.strategy}</Tag>
        </Descriptions.Item>
        <Descriptions.Item label="发布配置">
          <Space>
            <Text>最大激增: {containerConfig.rollout_config?.max_surge}</Text>
            <Text>最大不可用: {containerConfig.rollout_config?.max_unavailable}</Text>
          </Space>
        </Descriptions.Item>
      </Descriptions>

      {/* 编辑模态框 */}
      <Modal
        title="编辑容器配置"
        open={editModalVisible}
        onCancel={handleCancel}
        footer={[
          <Button key="cancel" onClick={handleCancel}>
            取消
          </Button>,
          <Button 
            key="save" 
            type="primary" 
            loading={saving}
            onClick={handleSave}
            icon={<SaveOutlined />}
          >
            保存
          </Button>
        ]}
        width={800}
        destroyOnClose
      >
        <Form
          form={form}
          layout="vertical"
          initialValues={containerConfig}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="replicas"
                label="副本数"
                rules={[
                  { required: true, message: '请输入副本数' },
                  { type: 'integer', min: 1, max: 10, message: '副本数必须在1-10之间' }
                ]}
              >
                <InputNumber 
                  min={1} 
                  max={10} 
                  style={{ width: '100%' }}
                  placeholder="请输入副本数"
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="namespace"
                label="命名空间"
                rules={[{ required: true, message: '请输入命名空间' }]}
              >
                <Input placeholder="请输入命名空间" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="base_image"
            label="基础镜像"
            rules={[{ required: true, message: '请输入基础镜像' }]}
          >
            <Input placeholder="请输入基础镜像" />
          </Form.Item>

          <Form.Item
            name="strategy"
            label="部署策略"
            rules={[{ required: true, message: '请选择部署策略' }]}
          >
            <Select placeholder="请选择部署策略">
              <Option value="RollingUpdate">滚动更新</Option>
              <Option value="Recreate">重新创建</Option>
            </Select>
          </Form.Item>

          <Divider orientation="left">资源配置</Divider>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name={['resources', 'requests', 'cpu']}
                label="CPU请求"
                rules={[{ required: true, message: '请输入CPU请求' }]}
              >
                <Input placeholder="如: 500m" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name={['resources', 'limits', 'cpu']}
                label="CPU限制"
                rules={[{ required: true, message: '请输入CPU限制' }]}
              >
                <Input placeholder="如: 1000m" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name={['resources', 'requests', 'memory']}
                label="内存请求"
                rules={[{ required: true, message: '请输入内存请求' }]}
              >
                <Input placeholder="如: 512Mi" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name={['resources', 'limits', 'memory']}
                label="内存限制"
                rules={[{ required: true, message: '请输入内存限制' }]}
              >
                <Input placeholder="如: 1Gi" />
              </Form.Item>
            </Col>
          </Row>

          <Divider orientation="left">发布配置</Divider>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name={['rollout_config', 'max_surge']}
                label="最大激增"
                rules={[{ required: true, message: '请输入最大激增' }]}
              >
                <Input placeholder="如: 25%" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name={['rollout_config', 'max_unavailable']}
                label="最大不可用"
                rules={[{ required: true, message: '请输入最大不可用' }]}
              >
                <Input placeholder="如: 25%" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name={['rollout_config', 'revision_history_limit']}
                label="历史版本限制"
              >
                <InputNumber 
                  min={1} 
                  max={100} 
                  style={{ width: '100%' }}
                  placeholder="默认10"
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name={['rollout_config', 'timeout_seconds']}
                label="超时时间(秒)"
              >
                <InputNumber 
                  min={60} 
                  max={3600} 
                  style={{ width: '100%' }}
                  placeholder="默认600"
                />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>
    </div>
  );
};

export default ContainerConfigTooltip; 