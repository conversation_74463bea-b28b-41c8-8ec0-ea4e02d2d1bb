import React, { useState, useMemo } from 'react';
import {
  Button, Descriptions, Typography, Space, Form, InputNumber, Input, Modal,
  Row, Col, Divider, Select, Tag, Card, Tooltip, Alert, Spin, message
} from 'antd';
import {
  EditOutlined, SaveOutlined, CloseOutlined, PlusOutlined, DeleteOutlined,
  CopyOutlined, InfoCircleOutlined, CheckCircleOutlined, ExclamationCircleOutlined,
  CloudServerOutlined, SettingOutlined, AppstoreOutlined, CloudOutlined
} from '@ant-design/icons';
import useContainerConfig from '@/hooks/useContainerConfig';
import './ContainerConfigTooltip.less';

const { Title, Text } = Typography;
const { Option } = Select;

interface ContainerConfigTooltipProps {
  onEdit?: () => void;
  editable?: boolean;
  onSave?: (updatedSettings: any) => Promise<boolean>;
}

const ContainerConfigTooltip: React.FC<ContainerConfigTooltipProps> = ({
  onEdit,
  editable = false
}) => {
  const { 
    containerConfig, 
    loading, 
    saveContainerConfig, 
    setContainerConfig 
  } = useContainerConfig();
  
  const [isEditing, setIsEditing] = useState(false);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [form] = Form.useForm<API.Container>();
  const [saving, setSaving] = useState(false);

  // 处理编辑按钮点击
  const handleEdit = () => {
    if (onEdit) {
      onEdit();
    } else {
      if (containerConfig) {
        // 设置表单初始值
        form.setFieldsValue({
          ...containerConfig,
          // 确保端口配置正确设置
          ports: containerConfig.ports || [{ name: 'http', container_port: 8080, service_port: 8080, protocol: 'TCP' }],
          // 确保资源配置正确设置
          resources: containerConfig.resources || {
            requests: { cpu: '500m', memory: '512Mi' },
            limits: { cpu: '1000m', memory: '1Gi' }
          }
        });
        setEditModalVisible(true);
        setIsEditing(true);
      }
    }
  };

  // 处理保存
  const handleSave = async () => {
    try {
      setSaving(true);
      const values = await form.validateFields();
      
      const success = await saveContainerConfig(values);
      if (success) {
        setEditModalVisible(false);
        setIsEditing(false);
      }
    } catch (error) {
      console.error('保存容器配置失败:', error);
    } finally {
      setSaving(false);
    }
  };

  // 处理取消
  const handleCancel = () => {
    setEditModalVisible(false);
    setIsEditing(false);
    form.resetFields();
  };

  // 可复制的内容组件
  const CopyableContent: React.FC<{text: string, label: string}> = ({text, label}) => {
    const handleCopy = () => {
      if (navigator.clipboard) {
        navigator.clipboard.writeText(text)
          .then(() => message.success(`${label}已复制到剪贴板`))
          .catch(() => message.error('复制失败，请手动复制'));
      } else {
        // 兼容方案
        const textarea = document.createElement('textarea');
        textarea.value = text;
        document.body.appendChild(textarea);
        textarea.select();
        try {
          document.execCommand('copy');
          message.success(`${label}已复制到剪贴板`);
        } catch (err) {
          message.error('复制失败，请手动复制');
        }
        document.body.removeChild(textarea);
      }
    };

    return (
      <div className="copyable-content">
        <div style={{ flex: 1, overflow: 'hidden', textOverflow: 'ellipsis' }}>
          {text || <Text type="secondary">未设置</Text>}
        </div>
        {text && (
          <Tooltip title={`复制${label}`}>
            <Button
              type="text"
              icon={<CopyOutlined />}
              onClick={handleCopy}
              size="small"
              className="copy-button"
            />
          </Tooltip>
        )}
      </div>
    );
  };

  // 格式化资源配置显示
  const formatResourceConfig = (requests: API.ResourceSpec, limits: API.ResourceSpec) => (
    <div style={{ display: 'flex', flexDirection: 'column', gap: 8 }}>
      <div style={{
        display: 'flex',
        alignItems: 'center',
        padding: '8px 12px',
        backgroundColor: '#f6ffed',
        borderRadius: 6,
        border: '1px solid #b7eb8f'
      }}>
        <CheckCircleOutlined style={{ color: '#52c41a', marginRight: 8 }} />
        <div>
          <Text strong style={{ color: '#389e0d' }}>请求资源:</Text>
          <div style={{ marginTop: 4 }}>
            <Tag color="green">CPU: {requests.cpu}</Tag>
            <Tag color="green">内存: {requests.memory}</Tag>
          </div>
        </div>
      </div>
      <div style={{
        display: 'flex',
        alignItems: 'center',
        padding: '8px 12px',
        backgroundColor: '#fff2e8',
        borderRadius: 6,
        border: '1px solid #ffbb96'
      }}>
        <ExclamationCircleOutlined style={{ color: '#fa8c16', marginRight: 8 }} />
        <div>
          <Text strong style={{ color: '#d46b08' }}>限制资源:</Text>
          <div style={{ marginTop: 4 }}>
            <Tag color="orange">CPU: {limits.cpu}</Tag>
            <Tag color="orange">内存: {limits.memory}</Tag>
          </div>
        </div>
      </div>
    </div>
  );

  // 格式化端口配置显示
  const formatPortsConfig = (ports: API.PortConfig[]) => (
    <Space direction="vertical" size="small" style={{ width: '100%' }}>
      {ports.map((port, index) => (
        <div key={index} className="port-config-item">
          <AppstoreOutlined className="port-icon" />
          <div className="port-info">
            <Tag color="blue">{port.name}</Tag>
            <Text code>{port.container_port}:{port.service_port}</Text>
            <Tag color="cyan">{port.protocol}</Tag>
          </div>
        </div>
      ))}
    </Space>
  );

  // 计算配置状态
  const configStatus = useMemo(() => {
    if (!containerConfig) return { status: 'error' as const, message: '配置未找到' };

    const issues = [];
    if (!containerConfig.base_image) issues.push('基础镜像未设置');
    if (!containerConfig.namespace) issues.push('命名空间未设置');
    if (!containerConfig.ports || containerConfig.ports.length === 0) issues.push('端口配置为空');
    if (!containerConfig.resources) issues.push('资源配置未设置');

    if (issues.length > 0) {
      return { status: 'warning' as const, message: `配置不完整: ${issues.join(', ')}` };
    }

    return { status: 'success' as const, message: '配置完整' };
  }, [containerConfig]);

  if (loading) {
    return (
      <Card
        variant="borderless"
        className="container-config-card"
        style={{ backgroundColor: '#fcfcfc' }}
      >
        <div className="loading-container">
          <Spin size="large" />
          <div className="loading-text">容器配置加载中...</div>
        </div>
      </Card>
    );
  }

  if (!containerConfig) {
    return (
      <Card
        variant="borderless"
        className="container-config-card"
        style={{ backgroundColor: '#fcfcfc' }}
      >
        <div className="empty-container">
          <ExclamationCircleOutlined className="empty-icon" />
          <div className="empty-text">容器配置未找到</div>
        </div>
      </Card>
    );
  }

  return (
    <Card
      variant="borderless"
      className="container-config-card"
      style={{ backgroundColor: '#fcfcfc' }}
      styles={{ body: { padding: '0px' } }}
    >
      {/* 头部区域 */}
      <div style={{
        padding: '12px 16px',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        borderBottom: '1px solid #f0f0f0',
        backgroundColor: '#fafafa'
      }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
          <CloudServerOutlined style={{ color: '#1890ff', fontSize: 16 }} />
          <Title level={5} style={{ margin: 0 }}>
            容器配置
          </Title>
          <Tooltip title={configStatus.message}>
            {configStatus.status === 'success' && <CheckCircleOutlined style={{ color: '#52c41a' }} />}
            {configStatus.status === 'warning' && <ExclamationCircleOutlined style={{ color: '#faad14' }} />}
            {configStatus.status === 'error' && <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />}
          </Tooltip>
        </div>
        {editable && (
          <Button
            type="primary"
            size="small"
            icon={<EditOutlined />}
            onClick={handleEdit}
          >
            编辑配置
          </Button>
        )}
      </div>

      {/* 状态提示 */}
      {configStatus.status !== 'success' && (
        <div style={{ padding: '12px 16px' }}>
          <Alert
            message={configStatus.message}
            type={configStatus.status}
            showIcon
            style={{ fontSize: '12px' }}
          />
        </div>
      )}

      {/* 配置内容 */}
      <div style={{ padding: '12px' }}>
        <Descriptions
          column={2}
          size="small"
          bordered
          style={{ backgroundColor: 'white' }}
          labelStyle={{
            backgroundColor: '#fafafa',
            fontWeight: 500,
            width: '100px',
            padding: '8px 12px'
          }}
          contentStyle={{
            backgroundColor: 'white',
            padding: '8px 12px'
          }}
        >
          <Descriptions.Item
            label={
              <div style={{ display: 'flex', alignItems: 'center', gap: 4 }}>
                <SettingOutlined />
                副本数
              </div>
            }
          >
            <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
              <Tag color="blue" style={{ margin: 0 }}>{containerConfig.replicas}</Tag>
              <Text type="secondary">个实例</Text>
            </div>
          </Descriptions.Item>

          <Descriptions.Item
            label={
              <div style={{ display: 'flex', alignItems: 'center', gap: 4 }}>
                <AppstoreOutlined />
                命名空间
              </div>
            }
          >
            <CopyableContent text={containerConfig.namespace} label="命名空间" />
          </Descriptions.Item>

          <Descriptions.Item
            label={
              <div style={{ display: 'flex', alignItems: 'center', gap: 4 }}>
                <SettingOutlined />
                部署策略
              </div>
            }
          >
            <Tag color="green" style={{ margin: 0 }}>
              {containerConfig.strategy === 'RollingUpdate' ? '滚动更新' : containerConfig.strategy}
            </Tag>
          </Descriptions.Item>

          <Descriptions.Item
            label={
              <div style={{ display: 'flex', alignItems: 'center', gap: 4 }}>
                <CloudOutlined />
                基础镜像
              </div>
            }
            span={2}
          >
            <div style={{
              background: '#f6ffed',
              padding: '6px 10px',
              borderRadius: '4px',
              border: '1px solid #b7eb8f',
              fontFamily: 'Monaco, Consolas, monospace',
              fontSize: '12px',
              wordBreak: 'break-all'
            }}>
              <CopyableContent text={containerConfig.base_image} label="基础镜像" />
            </div>
          </Descriptions.Item>

          <Descriptions.Item
            label={
              <div style={{ display: 'flex', alignItems: 'center', gap: 4 }}>
                <AppstoreOutlined />
                端口配置
              </div>
            }
            span={2}
          >
            <div style={{ display: 'flex', flexWrap: 'wrap', gap: 8 }}>
              {(containerConfig.ports || []).map((port, index) => (
                <div key={index} style={{
                  display: 'flex',
                  alignItems: 'center',
                  padding: '4px 8px',
                  backgroundColor: '#f0f5ff',
                  borderRadius: '4px',
                  border: '1px solid #adc6ff',
                  fontSize: '12px'
                }}>
                  <AppstoreOutlined style={{ color: '#1890ff', marginRight: 4, fontSize: '12px' }} />
                  <Tag color="blue" style={{ margin: 0, marginRight: 4, fontSize: '11px', padding: '0 4px', lineHeight: '16px' }}>{port.name}</Tag>
                  <Text code style={{ fontSize: '11px' }}>{port.container_port}:{port.service_port}</Text>
                  <Tag color="cyan" style={{ margin: 0, marginLeft: 4, fontSize: '11px', padding: '0 4px', lineHeight: '16px' }}>{port.protocol}</Tag>
                </div>
              ))}
            </div>
          </Descriptions.Item>

          <Descriptions.Item
            label={
              <div style={{ display: 'flex', alignItems: 'center', gap: 4 }}>
                <CloudServerOutlined />
                资源配置
              </div>
            }
            span={2}
          >
            {containerConfig.resources ? (
              <div style={{ display: 'flex', gap: 12 }}>
                <div style={{
                  flex: 1,
                  padding: '6px 10px',
                  backgroundColor: '#f6ffed',
                  borderRadius: '4px',
                  border: '1px solid #b7eb8f'
                }}>
                  <div style={{ display: 'flex', alignItems: 'center', marginBottom: 4 }}>
                    <CheckCircleOutlined style={{ color: '#52c41a', marginRight: 4, fontSize: '12px' }} />
                    <Text strong style={{ color: '#389e0d', fontSize: '12px' }}>请求</Text>
                  </div>
                  <div style={{ display: 'flex', gap: 4 }}>
                    <Tag color="green" style={{ fontSize: '11px', padding: '0 4px', lineHeight: '16px' }}>CPU: {containerConfig.resources.requests.cpu}</Tag>
                    <Tag color="green" style={{ fontSize: '11px', padding: '0 4px', lineHeight: '16px' }}>内存: {containerConfig.resources.requests.memory}</Tag>
                  </div>
                </div>
                <div style={{
                  flex: 1,
                  padding: '6px 10px',
                  backgroundColor: '#fff2e8',
                  borderRadius: '4px',
                  border: '1px solid #ffbb96'
                }}>
                  <div style={{ display: 'flex', alignItems: 'center', marginBottom: 4 }}>
                    <ExclamationCircleOutlined style={{ color: '#fa8c16', marginRight: 4, fontSize: '12px' }} />
                    <Text strong style={{ color: '#d46b08', fontSize: '12px' }}>限制</Text>
                  </div>
                  <div style={{ display: 'flex', gap: 4 }}>
                    <Tag color="orange" style={{ fontSize: '11px', padding: '0 4px', lineHeight: '16px' }}>CPU: {containerConfig.resources.limits.cpu}</Tag>
                    <Tag color="orange" style={{ fontSize: '11px', padding: '0 4px', lineHeight: '16px' }}>内存: {containerConfig.resources.limits.memory}</Tag>
                  </div>
                </div>
              </div>
            ) : (
              <Text type="secondary">未配置</Text>
            )}
          </Descriptions.Item>

          <Descriptions.Item
            label={
              <div style={{ display: 'flex', alignItems: 'center', gap: 4 }}>
                <SettingOutlined />
                发布配置
              </div>
            }
            span={2}
          >
            <div style={{ display: 'flex', flexWrap: 'wrap', gap: 6 }}>
              <Tag color="blue" style={{ fontSize: '11px', padding: '0 4px', lineHeight: '16px' }}>最大激增: {containerConfig.rollout_config?.max_surge}</Tag>
              <Tag color="orange" style={{ fontSize: '11px', padding: '0 4px', lineHeight: '16px' }}>最大不可用: {containerConfig.rollout_config?.max_unavailable}</Tag>
              {containerConfig.rollout_config?.revision_history_limit && (
                <Tag color="purple" style={{ fontSize: '11px', padding: '0 4px', lineHeight: '16px' }}>历史版本: {containerConfig.rollout_config.revision_history_limit}</Tag>
              )}
              {containerConfig.rollout_config?.timeout_seconds && (
                <Tag color="cyan" style={{ fontSize: '11px', padding: '0 4px', lineHeight: '16px' }}>超时时间: {containerConfig.rollout_config.timeout_seconds}s</Tag>
              )}
            </div>
          </Descriptions.Item>
        </Descriptions>
      </div>

      {/* 编辑模态框 */}
      <Modal
        title={
          <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
            <SettingOutlined style={{ color: '#1890ff' }} />
            编辑容器配置
          </div>
        }
        open={editModalVisible}
        onCancel={handleCancel}
        footer={[
          <Button key="cancel" onClick={handleCancel} icon={<CloseOutlined />}>
            取消
          </Button>,
          <Button
            key="save"
            type="primary"
            loading={saving}
            onClick={handleSave}
            icon={<SaveOutlined />}
          >
            保存配置
          </Button>
        ]}
        width={1000}
        maskClosable={false}
        styles={{
          header: {
            backgroundColor: '#fafafa',
            borderBottom: '1px solid #f0f0f0'
          },
          body: {
            padding: '16px',
            backgroundColor: '#fcfcfc'
          }
        }}
      >
        <Form
          form={form}
          layout="vertical"
          initialValues={containerConfig}
          requiredMark="optional"
        >
          {/* 基础配置 */}
          <Card
            title={
              <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                <SettingOutlined style={{ color: '#1890ff' }} />
                基础配置
              </div>
            }
            size="small"
            className="form-section-card"
          >
            <Row gutter={16}>
              <Col span={8}>
                <Form.Item
                  name="replicas"
                  label={
                    <div style={{ display: 'flex', alignItems: 'center', gap: 4 }}>
                      副本数
                      <Tooltip title="建议根据业务负载设置合适的副本数，通常1-5个">
                        <InfoCircleOutlined style={{ color: '#1890ff' }} />
                      </Tooltip>
                    </div>
                  }
                  rules={[
                    { required: true, message: '请输入副本数' },
                    { type: 'integer', min: 1, max: 10, message: '副本数必须在1-10之间' }
                  ]}
                  style={{ marginBottom: 16 }}
                >
                  <InputNumber
                    min={1}
                    max={10}
                    style={{ width: '100%' }}
                    placeholder="请输入副本数"
                    addonAfter="个"
                  />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  name="namespace"
                  label={
                    <div style={{ display: 'flex', alignItems: 'center', gap: 4 }}>
                      命名空间
                      <Tooltip title="Kubernetes命名空间，用于资源隔离">
                        <InfoCircleOutlined style={{ color: '#1890ff' }} />
                      </Tooltip>
                    </div>
                  }
                  rules={[
                    { required: true, message: '请输入命名空间' },
                    { pattern: /^[a-z0-9]([-a-z0-9]*[a-z0-9])?$/, message: '命名空间格式不正确' }
                  ]}
                  style={{ marginBottom: 16 }}
                >
                  <Input placeholder="如: default, production" />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  name="strategy"
                  label={
                    <div style={{ display: 'flex', alignItems: 'center', gap: 4 }}>
                      部署策略
                      <Tooltip title="选择合适的部署策略以确保服务稳定性">
                        <InfoCircleOutlined style={{ color: '#1890ff' }} />
                      </Tooltip>
                    </div>
                  }
                  rules={[{ required: true, message: '请选择部署策略' }]}
                  style={{ marginBottom: 16 }}
                >
                  <Select placeholder="请选择部署策略">
                    <Option value="RollingUpdate">滚动更新</Option>
                    <Option value="Recreate">重新创建</Option>
                  </Select>
                </Form.Item>
              </Col>
            </Row>

            <Form.Item
              name="base_image"
              label={
                <div style={{ display: 'flex', alignItems: 'center', gap: 4 }}>
                  基础镜像
                  <Tooltip title="容器运行的基础镜像，建议使用官方镜像">
                    <InfoCircleOutlined style={{ color: '#1890ff' }} />
                  </Tooltip>
                </div>
              }
              rules={[{ required: true, message: '请输入基础镜像' }]}
              style={{ marginBottom: 16 }}
            >
              <Input
                placeholder="如: openjdk:17-jre-slim, nginx:alpine"
                addonBefore={<CloudOutlined />}
              />
            </Form.Item>
          </Card>

          {/* 资源配置 */}
          <Card
            title={
              <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                <CloudServerOutlined style={{ color: '#52c41a' }} />
                资源配置
              </div>
            }
            size="small"
            className="form-section-card"
          >
            <Alert
              message="资源配置说明"
              description="请求资源是容器启动的最小资源要求，限制资源是容器可使用的最大资源。建议限制资源设置为请求资源的1.5-2倍。"
              type="info"
              showIcon
              style={{ marginBottom: 16 }}
            />

            <Row gutter={16}>
              <Col span={12}>
                <div className="resource-config-card requests" style={{ padding: '12px', marginBottom: 16 }}>
                  <Text strong style={{ color: '#389e0d', display: 'block', marginBottom: 12, fontSize: '13px' }}>
                    <CheckCircleOutlined style={{ marginRight: 4 }} />
                    请求资源 (最小保证)
                  </Text>

                  <Row gutter={8}>
                    <Col span={12}>
                      <Form.Item
                        name={['resources', 'requests', 'cpu']}
                        label="CPU"
                        rules={[
                          { required: true, message: '请输入CPU请求' },
                          { pattern: /^\d+m?$/, message: 'CPU格式不正确' }
                        ]}
                        style={{ marginBottom: 8 }}
                      >
                        <Input placeholder="500m" size="small" />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item
                        name={['resources', 'requests', 'memory']}
                        label="内存"
                        rules={[
                          { required: true, message: '请输入内存请求' },
                          { pattern: /^\d+(Mi|Gi|M|G)$/, message: '内存格式不正确' }
                        ]}
                        style={{ marginBottom: 8 }}
                      >
                        <Input placeholder="512Mi" size="small" />
                      </Form.Item>
                    </Col>
                  </Row>
                </div>
              </Col>

              <Col span={12}>
                <div className="resource-config-card limits" style={{ padding: '12px', marginBottom: 16 }}>
                  <Text strong style={{ color: '#d46b08', display: 'block', marginBottom: 12, fontSize: '13px' }}>
                    <ExclamationCircleOutlined style={{ marginRight: 4 }} />
                    限制资源 (最大使用)
                  </Text>

                  <Row gutter={8}>
                    <Col span={12}>
                      <Form.Item
                        name={['resources', 'limits', 'cpu']}
                        label="CPU"
                        rules={[
                          { required: true, message: '请输入CPU限制' },
                          { pattern: /^\d+m?$/, message: 'CPU格式不正确' }
                        ]}
                        style={{ marginBottom: 8 }}
                      >
                        <Input placeholder="1000m" size="small" />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item
                        name={['resources', 'limits', 'memory']}
                        label="内存"
                        rules={[
                          { required: true, message: '请输入内存限制' },
                          { pattern: /^\d+(Mi|Gi|M|G)$/, message: '内存格式不正确' }
                        ]}
                        style={{ marginBottom: 8 }}
                      >
                        <Input placeholder="1Gi" size="small" />
                      </Form.Item>
                    </Col>
                  </Row>
                </div>
              </Col>
            </Row>
          </Card>

          {/* 发布配置 */}
          <Card
            title={
              <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                <SettingOutlined style={{ color: '#722ed1' }} />
                发布配置
              </div>
            }
            size="small"
            className="form-section-card"
          >
            <Alert
              message="发布配置说明"
              description="控制滚动更新过程中的行为，最大激增和最大不可用决定了更新的速度和稳定性。"
              type="info"
              showIcon
              style={{ marginBottom: 16 }}
            />

            <Row gutter={16}>
              <Col span={6}>
                <Form.Item
                  name={['rollout_config', 'max_surge']}
                  label="最大激增"
                  rules={[
                    { required: true, message: '请输入最大激增' },
                    { pattern: /^(\d+%?|\d+)$/, message: '格式不正确' }
                  ]}
                  style={{ marginBottom: 16 }}
                >
                  <Input placeholder="25%" size="small" />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item
                  name={['rollout_config', 'max_unavailable']}
                  label="最大不可用"
                  rules={[
                    { required: true, message: '请输入最大不可用' },
                    { pattern: /^(\d+%?|\d+)$/, message: '格式不正确' }
                  ]}
                  style={{ marginBottom: 16 }}
                >
                  <Input placeholder="25%" size="small" />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item
                  name={['rollout_config', 'revision_history_limit']}
                  label="历史版本"
                  rules={[
                    { type: 'integer', min: 1, max: 100, message: '必须在1-100之间' }
                  ]}
                  style={{ marginBottom: 16 }}
                >
                  <InputNumber
                    min={1}
                    max={100}
                    style={{ width: '100%' }}
                    placeholder="10"
                    size="small"
                  />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item
                  name={['rollout_config', 'timeout_seconds']}
                  label="超时时间(秒)"
                  rules={[
                    { type: 'integer', min: 60, max: 3600, message: '必须在60-3600秒之间' }
                  ]}
                  style={{ marginBottom: 16 }}
                >
                  <InputNumber
                    min={60}
                    max={3600}
                    style={{ width: '100%' }}
                    placeholder="600"
                    size="small"
                  />
                </Form.Item>
              </Col>
            </Row>
          </Card>
        </Form>
      </Modal>
    </Card>
  );
};

export default ContainerConfigTooltip; 