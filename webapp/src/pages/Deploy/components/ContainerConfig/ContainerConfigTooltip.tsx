import React, { useState, useMemo } from 'react';
import {
  Button, Descriptions, Typography, Form, InputNumber, Input, Modal,
  Row, Col, Select, Card, Tooltip, Spin, message
} from 'antd';
import {
  EditOutlined, CopyOutlined, ExclamationCircleOutlined
} from '@ant-design/icons';
import useContainerConfig from '@/hooks/useContainerConfig';
import './ContainerConfigTooltip.less';

const { Title, Text } = Typography;
const { Option } = Select;

interface ContainerConfigTooltipProps {
  onEdit?: () => void;
  editable?: boolean;
  onSave?: (updatedSettings: any) => Promise<boolean>;
}

const ContainerConfigTooltip: React.FC<ContainerConfigTooltipProps> = ({
  onEdit,
  editable = false
}) => {
  const { 
    containerConfig, 
    loading, 
    saveContainerConfig, 
    setContainerConfig 
  } = useContainerConfig();
  
  const [isEditing, setIsEditing] = useState(false);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [form] = Form.useForm<API.Container>();
  const [saving, setSaving] = useState(false);

  // 处理编辑按钮点击
  const handleEdit = () => {
    if (onEdit) {
      onEdit();
    } else {
      if (containerConfig) {
        // 设置表单初始值
        form.setFieldsValue({
          ...containerConfig,
          // 确保端口配置正确设置
          ports: containerConfig.ports || [{ name: 'http', container_port: 8080, service_port: 8080, protocol: 'TCP' }],
          // 确保资源配置正确设置
          resources: containerConfig.resources || {
            requests: { cpu: '500m', memory: '512Mi' },
            limits: { cpu: '1000m', memory: '1Gi' }
          }
        });
        setEditModalVisible(true);
        setIsEditing(true);
      }
    }
  };

  // 处理保存
  const handleSave = async () => {
    try {
      setSaving(true);
      const values = await form.validateFields();
      
      const success = await saveContainerConfig(values);
      if (success) {
        setEditModalVisible(false);
        setIsEditing(false);
      }
    } catch (error) {
      console.error('保存容器配置失败:', error);
    } finally {
      setSaving(false);
    }
  };

  // 处理取消
  const handleCancel = () => {
    setEditModalVisible(false);
    setIsEditing(false);
    form.resetFields();
  };

  // 可复制的内容组件
  const CopyableContent: React.FC<{text: string, label: string}> = ({text, label}) => {
    const handleCopy = () => {
      if (navigator.clipboard) {
        navigator.clipboard.writeText(text)
          .then(() => message.success(`${label}已复制到剪贴板`))
          .catch(() => message.error('复制失败，请手动复制'));
      } else {
        // 兼容方案
        const textarea = document.createElement('textarea');
        textarea.value = text;
        document.body.appendChild(textarea);
        textarea.select();
        try {
          document.execCommand('copy');
          message.success(`${label}已复制到剪贴板`);
        } catch (err) {
          message.error('复制失败，请手动复制');
        }
        document.body.removeChild(textarea);
      }
    };

    return (
      <div className="copyable-content">
        <div style={{ flex: 1, overflow: 'hidden', textOverflow: 'ellipsis' }}>
          {text || <Text type="secondary">未设置</Text>}
        </div>
        {text && (
          <Tooltip title={`复制${label}`}>
            <Button
              type="text"
              icon={<CopyOutlined />}
              onClick={handleCopy}
              size="small"
              className="copy-button"
            />
          </Tooltip>
        )}
      </div>
    );
  };

  // 格式化资源配置显示
  const formatResourceConfig = (requests: API.ResourceSpec, limits: API.ResourceSpec) => (
    <div style={{ display: 'flex', gap: 16 }}>
      <div style={{
        flex: 1,
        padding: '8px 12px',
        backgroundColor: '#fafafa',
        border: '1px solid #d9d9d9',
        borderRadius: 4
      }}>
        <div style={{ marginBottom: 4 }}>
          <Text strong style={{ fontSize: '12px' }}>请求资源</Text>
        </div>
        <div>
          <Text style={{ fontSize: '12px' }}>CPU: {requests.cpu}</Text>
          <br />
          <Text style={{ fontSize: '12px' }}>内存: {requests.memory}</Text>
        </div>
      </div>
      <div style={{
        flex: 1,
        padding: '8px 12px',
        backgroundColor: '#fafafa',
        border: '1px solid #d9d9d9',
        borderRadius: 4
      }}>
        <div style={{ marginBottom: 4 }}>
          <Text strong style={{ fontSize: '12px' }}>限制资源</Text>
        </div>
        <div>
          <Text style={{ fontSize: '12px' }}>CPU: {limits.cpu}</Text>
          <br />
          <Text style={{ fontSize: '12px' }}>内存: {limits.memory}</Text>
        </div>
      </div>
    </div>
  );

  // 格式化端口配置显示
  const formatPortsConfig = (ports: API.PortConfig[]) => (
    <div style={{ display: 'flex', flexWrap: 'wrap', gap: 8 }}>
      {ports.map((port, index) => (
        <div key={index} style={{
          padding: '4px 8px',
          backgroundColor: '#fafafa',
          border: '1px solid #d9d9d9',
          borderRadius: 4,
          fontSize: '12px'
        }}>
          <Text strong>{port.name}</Text>
          <Text style={{ margin: '0 4px' }}>:</Text>
          <Text code style={{ fontSize: '11px' }}>{port.container_port}:{port.service_port}</Text>
          <Text style={{ margin: '0 4px' }}>({port.protocol})</Text>
        </div>
      ))}
    </div>
  );

  // 计算配置状态
  const configStatus = useMemo(() => {
    if (!containerConfig) return { status: 'error' as const, message: '配置未找到' };

    const issues = [];
    if (!containerConfig.base_image) issues.push('基础镜像未设置');
    if (!containerConfig.namespace) issues.push('命名空间未设置');
    if (!containerConfig.ports || containerConfig.ports.length === 0) issues.push('端口配置为空');
    if (!containerConfig.resources) issues.push('资源配置未设置');

    if (issues.length > 0) {
      return { status: 'warning' as const, message: `配置不完整: ${issues.join(', ')}` };
    }

    return { status: 'success' as const, message: '配置完整' };
  }, [containerConfig]);

  if (loading) {
    return (
      <Card
        variant="borderless"
        className="container-config-card"
        style={{ backgroundColor: '#fcfcfc' }}
      >
        <div className="loading-container">
          <Spin size="large" />
          <div className="loading-text">容器配置加载中...</div>
        </div>
      </Card>
    );
  }

  if (!containerConfig) {
    return (
      <Card
        variant="borderless"
        className="container-config-card"
        style={{ backgroundColor: '#fcfcfc' }}
      >
        <div className="empty-container">
          <ExclamationCircleOutlined className="empty-icon" />
          <div className="empty-text">容器配置未找到</div>
        </div>
      </Card>
    );
  }

  return (
    <Card
      variant="borderless"
      className="container-config-card"
      style={{ backgroundColor: '#fcfcfc' }}
      styles={{ body: { padding: '0px' } }}
    >
      {/* 头部区域 */}
      <div style={{
        padding: '12px 16px',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        borderBottom: '1px solid #e8e8e8',
        backgroundColor: '#ffffff'
      }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
          <Title level={5} style={{ margin: 0, color: '#262626' }}>
            容器配置
          </Title>
          {configStatus.status !== 'success' && (
            <Text type="secondary" style={{ fontSize: '12px' }}>
              ({configStatus.message})
            </Text>
          )}
        </div>
        {editable && (
          <Button
            type="primary"
            size="small"
            icon={<EditOutlined />}
            onClick={handleEdit}
          >
            编辑
          </Button>
        )}
      </div>



      {/* 配置内容 */}
      <div className="professional-layout" style={{ padding: '12px' }}>
        <Descriptions
          column={2}
          size="small"
          bordered
          style={{ backgroundColor: 'white' }}
          labelStyle={{
            backgroundColor: '#fafafa',
            fontWeight: 500,
            width: '100px',
            padding: '8px 12px'
          }}
          contentStyle={{
            backgroundColor: 'white',
            padding: '8px 12px'
          }}
        >
          <Descriptions.Item label="副本数">
            <Text>{containerConfig.replicas} 个实例</Text>
          </Descriptions.Item>

          <Descriptions.Item label="命名空间">
            <CopyableContent text={containerConfig.namespace} label="命名空间" />
          </Descriptions.Item>

          <Descriptions.Item label="部署策略">
            <Text>
              {containerConfig.strategy === 'RollingUpdate' ? '滚动更新' : containerConfig.strategy}
            </Text>
          </Descriptions.Item>

          <Descriptions.Item label="基础镜像" span={2}>
            <div style={{
              background: '#fafafa',
              padding: '6px 10px',
              borderRadius: '4px',
              border: '1px solid #d9d9d9',
              fontFamily: 'Monaco, Consolas, monospace',
              fontSize: '12px',
              wordBreak: 'break-all'
            }}>
              <CopyableContent text={containerConfig.base_image} label="基础镜像" />
            </div>
          </Descriptions.Item>

          <Descriptions.Item label="端口配置" span={2}>
            {formatPortsConfig(containerConfig.ports || [])}
          </Descriptions.Item>

          <Descriptions.Item label="资源配置" span={2}>
            {containerConfig.resources ?
              formatResourceConfig(containerConfig.resources.requests, containerConfig.resources.limits) :
              <Text type="secondary">未配置</Text>
            }
          </Descriptions.Item>

          <Descriptions.Item label="发布配置" span={2}>
            <div style={{ display: 'flex', flexWrap: 'wrap', gap: 8 }}>
              <Text style={{ fontSize: '12px' }}>最大激增: {containerConfig.rollout_config?.max_surge}</Text>
              <Text style={{ fontSize: '12px' }}>最大不可用: {containerConfig.rollout_config?.max_unavailable}</Text>
              {containerConfig.rollout_config?.revision_history_limit && (
                <Text style={{ fontSize: '12px' }}>历史版本: {containerConfig.rollout_config.revision_history_limit}</Text>
              )}
              {containerConfig.rollout_config?.timeout_seconds && (
                <Text style={{ fontSize: '12px' }}>超时时间: {containerConfig.rollout_config.timeout_seconds}s</Text>
              )}
            </div>
          </Descriptions.Item>
        </Descriptions>
      </div>

      {/* 编辑模态框 */}
      <Modal
        title="编辑容器配置"
        open={editModalVisible}
        onCancel={handleCancel}
        footer={[
          <Button key="cancel" onClick={handleCancel}>
            取消
          </Button>,
          <Button
            key="save"
            type="primary"
            loading={saving}
            onClick={handleSave}
          >
            保存
          </Button>
        ]}
        width={1000}
        maskClosable={false}
        styles={{
          header: {
            backgroundColor: '#ffffff',
            borderBottom: '1px solid #e8e8e8'
          },
          body: {
            padding: '24px',
            backgroundColor: '#ffffff'
          }
        }}
      >
        <div className="professional-layout">
          <Form
            form={form}
            layout="vertical"
            initialValues={containerConfig}
            requiredMark="optional"
          >
          {/* 基础配置 */}
          <Card
            title="基础配置"
            size="small"
            className="form-section-card"
            styles={{
              header: { backgroundColor: '#fafafa', borderBottom: '1px solid #e8e8e8' },
              body: { backgroundColor: '#ffffff' }
            }}
          >
            <Row gutter={16}>
              <Col span={8}>
                <Form.Item
                  name="replicas"
                  label="副本数"
                  rules={[
                    { required: true, message: '请输入副本数' },
                    { type: 'integer', min: 1, max: 10, message: '副本数必须在1-10之间' }
                  ]}
                  style={{ marginBottom: 16 }}
                >
                  <InputNumber
                    min={1}
                    max={10}
                    style={{ width: '100%' }}
                    placeholder="1"
                  />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  name="namespace"
                  label="命名空间"
                  rules={[
                    { required: true, message: '请输入命名空间' },
                    { pattern: /^[a-z0-9]([-a-z0-9]*[a-z0-9])?$/, message: '命名空间格式不正确' }
                  ]}
                  style={{ marginBottom: 16 }}
                >
                  <Input placeholder="default" />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  name="strategy"
                  label="部署策略"
                  rules={[{ required: true, message: '请选择部署策略' }]}
                  style={{ marginBottom: 16 }}
                >
                  <Select placeholder="请选择部署策略">
                    <Option value="RollingUpdate">滚动更新</Option>
                    <Option value="Recreate">重新创建</Option>
                  </Select>
                </Form.Item>
              </Col>
            </Row>

            <Form.Item
              name="base_image"
              label="基础镜像"
              rules={[{ required: true, message: '请输入基础镜像' }]}
              style={{ marginBottom: 16 }}
            >
              <Input placeholder="openjdk:17-jre-slim" />
            </Form.Item>
          </Card>

          {/* 资源配置 */}
          <Card
            title="资源配置"
            size="small"
            className="form-section-card"
            styles={{
              header: { backgroundColor: '#fafafa', borderBottom: '1px solid #e8e8e8' },
              body: { backgroundColor: '#ffffff' }
            }}
          >

            <Row gutter={16}>
              <Col span={12}>
                <div style={{
                  padding: '12px',
                  marginBottom: 16,
                  backgroundColor: '#fafafa',
                  border: '1px solid #d9d9d9',
                  borderRadius: 4
                }}>
                  <Text strong style={{ display: 'block', marginBottom: 12, fontSize: '13px' }}>
                    请求资源
                  </Text>

                  <Row gutter={8}>
                    <Col span={12}>
                      <Form.Item
                        name={['resources', 'requests', 'cpu']}
                        label="CPU"
                        rules={[
                          { required: true, message: '请输入CPU请求' },
                          { pattern: /^\d+m?$/, message: 'CPU格式不正确' }
                        ]}
                        style={{ marginBottom: 8 }}
                      >
                        <Input placeholder="500m" size="small" />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item
                        name={['resources', 'requests', 'memory']}
                        label="内存"
                        rules={[
                          { required: true, message: '请输入内存请求' },
                          { pattern: /^\d+(Mi|Gi|M|G)$/, message: '内存格式不正确' }
                        ]}
                        style={{ marginBottom: 8 }}
                      >
                        <Input placeholder="512Mi" size="small" />
                      </Form.Item>
                    </Col>
                  </Row>
                </div>
              </Col>

              <Col span={12}>
                <div style={{
                  padding: '12px',
                  marginBottom: 16,
                  backgroundColor: '#fafafa',
                  border: '1px solid #d9d9d9',
                  borderRadius: 4
                }}>
                  <Text strong style={{ display: 'block', marginBottom: 12, fontSize: '13px' }}>
                    限制资源
                  </Text>

                  <Row gutter={8}>
                    <Col span={12}>
                      <Form.Item
                        name={['resources', 'limits', 'cpu']}
                        label="CPU"
                        rules={[
                          { required: true, message: '请输入CPU限制' },
                          { pattern: /^\d+m?$/, message: 'CPU格式不正确' }
                        ]}
                        style={{ marginBottom: 8 }}
                      >
                        <Input placeholder="1000m" size="small" />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item
                        name={['resources', 'limits', 'memory']}
                        label="内存"
                        rules={[
                          { required: true, message: '请输入内存限制' },
                          { pattern: /^\d+(Mi|Gi|M|G)$/, message: '内存格式不正确' }
                        ]}
                        style={{ marginBottom: 8 }}
                      >
                        <Input placeholder="1Gi" size="small" />
                      </Form.Item>
                    </Col>
                  </Row>
                </div>
              </Col>
            </Row>
          </Card>

          {/* 发布配置 */}
          <Card
            title="发布配置"
            size="small"
            className="form-section-card"
            styles={{
              header: { backgroundColor: '#fafafa', borderBottom: '1px solid #e8e8e8' },
              body: { backgroundColor: '#ffffff' }
            }}
          >

            <Row gutter={16}>
              <Col span={6}>
                <Form.Item
                  name={['rollout_config', 'max_surge']}
                  label="最大激增"
                  rules={[
                    { required: true, message: '请输入最大激增' },
                    { pattern: /^(\d+%?|\d+)$/, message: '格式不正确' }
                  ]}
                  style={{ marginBottom: 16 }}
                >
                  <Input placeholder="25%" size="small" />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item
                  name={['rollout_config', 'max_unavailable']}
                  label="最大不可用"
                  rules={[
                    { required: true, message: '请输入最大不可用' },
                    { pattern: /^(\d+%?|\d+)$/, message: '格式不正确' }
                  ]}
                  style={{ marginBottom: 16 }}
                >
                  <Input placeholder="25%" size="small" />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item
                  name={['rollout_config', 'revision_history_limit']}
                  label="历史版本"
                  rules={[
                    { type: 'integer', min: 1, max: 100, message: '必须在1-100之间' }
                  ]}
                  style={{ marginBottom: 16 }}
                >
                  <InputNumber
                    min={1}
                    max={100}
                    style={{ width: '100%' }}
                    placeholder="10"
                    size="small"
                  />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item
                  name={['rollout_config', 'timeout_seconds']}
                  label="超时时间(秒)"
                  rules={[
                    { type: 'integer', min: 60, max: 3600, message: '必须在60-3600秒之间' }
                  ]}
                  style={{ marginBottom: 16 }}
                >
                  <InputNumber
                    min={60}
                    max={3600}
                    style={{ width: '100%' }}
                    placeholder="600"
                    size="small"
                  />
                </Form.Item>
              </Col>
            </Row>
          </Card>
        </Form>
      </Modal>
    </Card>
  );
};

export default ContainerConfigTooltip; 